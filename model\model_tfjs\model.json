{"format": "graph-model", "generatedBy": "2.18.0", "convertedBy": "TensorFlow.js Converter v4.22.0", "signature": {"inputs": {"inputs": {"name": "inputs:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "224"}, {"size": "224"}, {"size": "3"}]}}}, "outputs": {"output_0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "3"}]}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/sequential_1/conv2d_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1_2/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1_2/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/flatten_1/Reshape/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "200704"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1_2/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}, {"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "inputs", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "224"}, {"size": "224"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_1/Relu", "op": "_FusedConv2D", "input": ["inputs", "StatefulPartitionedCall/sequential_1/conv2d_1/convolution/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_1/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/max_pooling2d_1/MaxPool2d", "op": "MaxPool", "input": ["StatefulPartitionedCall/sequential_1/activation_1/Relu"], "attr": {"padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_1_2/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_1/max_pooling2d_1/MaxPool2d", "StatefulPartitionedCall/sequential_1/conv2d_1_2/convolution/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_1_2/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/max_pooling2d_1_2/MaxPool2d", "op": "MaxPool", "input": ["StatefulPartitionedCall/sequential_1/activation_1_2/Relu"], "attr": {"padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/flatten_1/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/sequential_1/max_pooling2d_1_2/MaxPool2d", "StatefulPartitionedCall/sequential_1/flatten_1/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_1/flatten_1/Reshape", "StatefulPartitionedCall/sequential_1/dense_1/Cast/ReadVariableOp", "StatefulPartitionedCall/sequential_1/dense_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1_2/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_1/dense_1/Relu", "StatefulPartitionedCall/sequential_1/dense_1_2/Cast/ReadVariableOp", "StatefulPartitionedCall/sequential_1/dense_1_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "leakyrelu_alpha": {"f": 0.2}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1_2/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/sequential_1/dense_1_2/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/sequential_1/dense_1_2/Softmax"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 1994}}, "weightsManifest": [{"paths": ["group1-shard1of25.bin", "group1-shard2of25.bin", "group1-shard3of25.bin", "group1-shard4of25.bin", "group1-shard5of25.bin", "group1-shard6of25.bin", "group1-shard7of25.bin", "group1-shard8of25.bin", "group1-shard9of25.bin", "group1-shard10of25.bin", "group1-shard11of25.bin", "group1-shard12of25.bin", "group1-shard13of25.bin", "group1-shard14of25.bin", "group1-shard15of25.bin", "group1-shard16of25.bin", "group1-shard17of25.bin", "group1-shard18of25.bin", "group1-shard19of25.bin", "group1-shard20of25.bin", "group1-shard21of25.bin", "group1-shard22of25.bin", "group1-shard23of25.bin", "group1-shard24of25.bin", "group1-shard25of25.bin"], "weights": [{"name": "StatefulPartitionedCall/sequential_1/conv2d_1/convolution/ReadVariableOp", "shape": [3, 3, 3, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/Squeeze", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1_2/convolution/ReadVariableOp", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1_2/Squeeze", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/flatten_1/Reshape/shape", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/Cast/ReadVariableOp", "shape": [200704, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/BiasAdd/ReadVariableOp", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_1_2/Cast/ReadVariableOp", "shape": [128, 3], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_1_2/BiasAdd/ReadVariableOp", "shape": [3], "dtype": "float32"}]}]}